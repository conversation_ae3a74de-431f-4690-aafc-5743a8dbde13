@value variables: "@styles/variables.module.css";
@value colorBlack, colorWhite, gray300, gray400, gray600, gray700, gray800, brandColorOne, brandColorTwo, brandColorThree, brandColorFour, brandColorFive from variables;
@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-xl-1440, breakpoint-lg, breakpoint-md, breakpoint-sm from breakpoints;

.container {
  min-height: 100vh;
  padding: 80px 124px;
  background-color: gray300;

  @media (max-width: breakpoint-xl-1440) {
    padding: 80px 60px;
  }

  @media (max-width: breakpoint-md) {
    padding: 40px 32px;
  }

  @media (max-width: breakpoint-sm) {
    padding: 40px 16px;
  }
}

.header {
  text-align: center;
  margin-bottom: 60px;
}

.title {
  font-size: 52px;
  font-weight: 600;
  line-height: 140%;
  letter-spacing: -1.04px;
  color: colorBlack;
  margin-bottom: 16px;

  @media (max-width: breakpoint-md) {
    font-size: 40px;
  }

  @media (max-width: breakpoint-sm) {
    font-size: 32px;
  }
}

.description {
  font-size: 20px;
  line-height: 150%;
  color: gray800;
  max-width: 600px;
  margin: 0 auto 24px;

  @media (max-width: breakpoint-sm) {
    font-size: 18px;
  }
}

.xmlSitemapLink {
  margin-top: 24px;
}

.xmlLink {
  display: inline-flex;
  align-items: center;
  padding: 12px 24px;
  background: linear-gradient(93deg, brandColorOne 0%, brandColorTwo 30.56%, brandColorThree 53.47%, brandColorFour 75.75%, brandColorFive 100%);
  color: colorWhite;
  text-decoration: none;
  border-radius: 8px;
  font-weight: 500;
  font-size: 16px;
  transition: transform 0.2s ease;
}

.xmlLink:hover {
  transform: translateY(-2px);
  color: colorWhite;
  text-decoration: none;
}

.categoriesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 40px;
  margin-bottom: 60px;

  @media (max-width: breakpoint-md) {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  @media (max-width: breakpoint-sm) {
    grid-template-columns: 1fr;
    gap: 24px;
  }
}

.categorySection {
  background: colorWhite;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid gray400;

  @media (max-width: breakpoint-sm) {
    padding: 24px;
  }
}

.categoryTitle {
  font-size: 24px;
  font-weight: 600;
  color: colorBlack;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.categoryCount {
  font-size: 14px;
  color: gray600;
  margin-bottom: 24px;
  font-weight: 500;
}

.urlList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.urlItem {
  border-bottom: 1px solid gray400;
  padding: 16px 0;
}

.urlItem:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.urlItem:first-child {
  padding-top: 0;
}

.urlLink {
  display: block;
  text-decoration: none;
  color: inherit;
  transition: all 0.2s ease;
}

.urlLink:hover {
  color: brandColorThree;
  text-decoration: none;
}

.urlLink:hover .urlPath {
  color: brandColorThree;
}

.urlPath {
  display: block;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  color: gray700;
  margin-bottom: 4px;
  word-break: break-all;
}

.urlName {
  display: block;
  font-size: 16px;
  font-weight: 500;
  color: colorBlack;
  margin-bottom: 8px;
}

.urlMeta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: gray600;

  @media (max-width: breakpoint-sm) {
    flex-direction: column;
    gap: 4px;
  }
}

.priority,
.changefreq {
  font-weight: 500;
}

.footer {
  text-align: center;
  padding-top: 40px;
  border-top: 2px solid gray400;
}

.stats {
  display: flex;
  justify-content: center;
  gap: 60px;
  margin-bottom: 24px;

  @media (max-width: breakpoint-sm) {
    gap: 40px;
  }
}

.statItem {
  text-align: center;
}

.statNumber {
  display: block;
  font-size: 32px;
  font-weight: 700;
  color: brandColorThree;
  line-height: 1;

  @media (max-width: breakpoint-sm) {
    font-size: 28px;
  }
}

.statLabel {
  display: block;
  font-size: 14px;
  color: gray700;
  margin-top: 4px;
  font-weight: 500;
}

.lastUpdated {
  font-size: 14px;
  color: gray600;
  margin: 0;
}
